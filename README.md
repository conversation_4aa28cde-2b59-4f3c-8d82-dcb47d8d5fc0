# Modern Audio Player

A modern audio player application built with Python and CustomTkinter featuring:

## Features
- Play/pause functionality for audio files
- Next/previous track navigation
- Loop modes (single track, playlist loop, no loop)
- Shuffle functionality for randomizing playlist order
- Playlist management (add, remove, reorder songs)
- Modern CustomTkinter UI with custom SVG icons
- Album artwork display with metadata extraction
- Support for MP3, WAV, FLAC, and other common audio formats

## Installation

1. Install Python 3.8 or higher
2. Install dependencies:
```bash
pip install -r requirements.txt
```

## Usage

Run the application:
```bash
python main.py
```

## Project Structure
```
├── main.py                 # Main application entry point
├── src/
│   ├── audio_engine.py     # Audio playback functionality
│   ├── metadata_handler.py # Metadata and album art extraction
│   ├── playlist_manager.py # Playlist management
│   ├── gui/
│   │   ├── main_window.py  # Main application window
│   │   └── playlist_view.py # Playlist view component
│   └── assets/
│       └── icons/          # SVG icons and assets
├── requirements.txt        # Python dependencies
└── README.md              # This file
```
